import React, { useState, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { Palette, Grid, List, Filter, ChevronDown, X, Sparkles } from 'lucide-react'
import MainLayout from '../../../components/layout/MainLayout'
import {
  ThemeCard,
  SearchBar,
  LoadingGrid,
  EmptyState
} from '../../../components/curated'
import { useThemes } from '../hooks'
import { Theme } from '../../../services/curatedService'
import { cn } from '../../../utils/cn'

/**
 * Atelier Page
 * Theme discovery and selection gallery
 * Displays all available themes with statistics and filtering
 */
const Atelier: React.FC = () => {
  const navigate = useNavigate()
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedCategory, setSelectedCategory] = useState<string>('')

  const {
    themes,
    loading,
    error,
    filters,
    setFilters,
    refetch,
    clearFilters
  } = useThemes()

  // Get unique categories from themes
  const categories = React.useMemo(() => {
    const cats = themes.map((theme: any) => theme.category).filter((cat: any): cat is string => typeof cat === 'string')
    return Array.from(new Set(cats)).sort()
  }, [themes])

  const handleSearch = useCallback((searchTerm: string) => {
    setFilters({ search: searchTerm, page: 1 })
  }, [setFilters])

  const handleCategoryFilter = useCallback((category: string) => {
    setSelectedCategory(category)
    setFilters({ category: category || undefined, page: 1 })
  }, [setFilters])

  const handleThemeClick = useCallback((theme: Theme) => {
    // Navigate to Anthology page filtered by this theme
    console.log('🎨 THEME CLICKED:')
    console.log('  Theme ID:', theme.id)
    console.log('  Theme Name:', theme.name_en)
    console.log('  Full Theme Object:', theme)

    const url = `/anthology?theme_id=${theme.id}`
    console.log('🎨 NAVIGATING TO:', url)

    navigate(url)
  }, [navigate])

  const handleClearFilters = useCallback(() => {
    setSelectedCategory('')
    clearFilters()
  }, [clearFilters])

  const hasActiveFilters = !!(filters.search || filters.category)

  // Top content (search and filters)
  const topContent = (
    <div className="space-y-4">
      {/* Search and view controls */}
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        <div className="flex-1 max-w-md">
          <SearchBar
            placeholder="Search themes..."
            value={filters.search || ''}
            onChange={handleSearch}
            className="w-full"
          />
        </div>

        <div className="flex items-center gap-4">
          {/* Active filters indicator and clear button */}
          {hasActiveFilters && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {[filters.search, filters.category].filter(Boolean).length} filters active
              </span>
              <button
                onClick={handleClearFilters}
                className="text-sm text-primary hover:text-primary/80 transition-colors"
              >
                Clear all
              </button>
            </div>
          )}

          {/* View mode toggle */}
          <div className="flex items-center border border-border rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={cn(
                'p-2 rounded transition-all duration-200',
                viewMode === 'grid'
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:text-foreground'
              )}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={cn(
                'p-2 rounded transition-all duration-200',
                viewMode === 'list'
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:text-foreground'
              )}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Modern Filter Panel */}
      <div className="bg-gradient-to-r from-purple-50/50 via-blue-50/50 to-indigo-50/50 dark:from-purple-950/20 dark:via-blue-950/20 dark:to-indigo-950/20 rounded-xl border border-purple-200/50 dark:border-purple-800/30 p-6 backdrop-blur-sm">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg">
            <Filter className="w-4 h-4 text-white" />
          </div>
          <h3 className="text-lg font-semibold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            Discover Themes
          </h3>
          <Sparkles className="w-4 h-4 text-purple-500 animate-pulse" />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Category Filter */}
          <div className="space-y-3">
            <label className="flex items-center gap-2 text-sm font-medium text-foreground">
              <Palette className="w-4 h-4 text-purple-500" />
              Category
            </label>
            <div className="relative">
              <select
                value={selectedCategory}
                onChange={(e) => handleCategoryFilter(e.target.value)}
                className={cn(
                  'w-full px-4 py-3 rounded-xl border-2 border-purple-200/50 dark:border-purple-800/30',
                  'bg-white/80 dark:bg-gray-900/80 text-foreground text-sm backdrop-blur-sm',
                  'focus:outline-none focus:ring-2 focus:ring-purple-500/30 focus:border-purple-500/50',
                  'hover:border-purple-300/70 dark:hover:border-purple-700/50',
                  'transition-all duration-300 ease-out',
                  'appearance-none cursor-pointer',
                  'shadow-sm hover:shadow-md'
                )}
              >
                <option value="">✨ All Categories</option>
                {categories.map((category: string) => (
                  <option key={category} value={category}>
                    🎨 {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-purple-400 pointer-events-none" />
            </div>
          </div>

          {/* Status Filter */}
          <div className="space-y-3">
            <label className="flex items-center gap-2 text-sm font-medium text-foreground">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              Status
            </label>
            <div className="relative">
              <select
                value={filters.is_active === undefined ? 'all' : filters.is_active ? 'active' : 'inactive'}
                onChange={(e) => {
                  const value = e.target.value
                  if (value === 'all') {
                    setFilters({ is_active: undefined })
                  } else {
                    setFilters({ is_active: value === 'active' })
                  }
                }}
                className={cn(
                  'w-full px-4 py-3 rounded-xl border-2 border-blue-200/50 dark:border-blue-800/30',
                  'bg-white/80 dark:bg-gray-900/80 text-foreground text-sm backdrop-blur-sm',
                  'focus:outline-none focus:ring-2 focus:ring-blue-500/30 focus:border-blue-500/50',
                  'hover:border-blue-300/70 dark:hover:border-blue-700/50',
                  'transition-all duration-300 ease-out',
                  'appearance-none cursor-pointer',
                  'shadow-sm hover:shadow-md'
                )}
              >
                <option value="all">🌟 All Themes</option>
                <option value="active">✅ Active Only</option>
                <option value="inactive">⏸️ Inactive Only</option>
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-blue-400 pointer-events-none" />
            </div>
          </div>

          {/* Sort Filter */}
          <div className="space-y-3">
            <label className="flex items-center gap-2 text-sm font-medium text-foreground">
              <div className="flex space-x-1">
                <div className="w-1 h-4 bg-indigo-500 rounded-full"></div>
                <div className="w-1 h-3 bg-indigo-400 rounded-full"></div>
                <div className="w-1 h-2 bg-indigo-300 rounded-full"></div>
              </div>
              Sort By
            </label>
            <div className="relative">
              <select
                className={cn(
                  'w-full px-4 py-3 rounded-xl border-2 border-indigo-200/50 dark:border-indigo-800/30',
                  'bg-white/80 dark:bg-gray-900/80 text-foreground text-sm backdrop-blur-sm',
                  'focus:outline-none focus:ring-2 focus:ring-indigo-500/30 focus:border-indigo-500/50',
                  'hover:border-indigo-300/70 dark:hover:border-indigo-700/50',
                  'transition-all duration-300 ease-out',
                  'appearance-none cursor-pointer',
                  'shadow-sm hover:shadow-md'
                )}
              >
                <option value="name">📝 Name (A-Z)</option>
                <option value="category">🏷️ Category</option>
                <option value="content_count">📊 Content Count</option>
                <option value="created_date">📅 Created Date</option>
              </select>
              <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-indigo-400 pointer-events-none" />
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="mt-6 pt-4 border-t border-purple-200/30 dark:border-purple-800/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-purple-600 dark:text-purple-400">Active Filters:</span>
                <div className="flex gap-2">
                  {filters.search && (
                    <span className="px-3 py-1 bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 rounded-full text-xs font-medium">
                      Search: "{filters.search}"
                    </span>
                  )}
                  {filters.category && (
                    <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium">
                      Category: {filters.category}
                    </span>
                  )}
                </div>
              </div>
              <button
                onClick={handleClearFilters}
                className="flex items-center gap-1 px-3 py-1 text-xs font-medium text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors"
              >
                <X className="w-3 h-3" />
                Clear All
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )

  // Bottom content (stats summary)
  const bottomContent = !loading && themes.length > 0 ? (
    <div className="p-4 bg-muted/50 rounded-lg">
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
        <div>
          <div className="text-2xl font-bold text-primary">{themes.length}</div>
          <div className="text-sm text-muted-foreground">Total Themes</div>
        </div>
        <div>
          <div className="text-2xl font-bold text-primary">{categories.length}</div>
          <div className="text-sm text-muted-foreground">Categories</div>
        </div>
        <div>
          <div className="text-2xl font-bold text-primary">
            {themes.reduce((sum: number, theme: any) => sum + (theme.statistics?.total_content_sets || 0), 0)}
          </div>
          <div className="text-sm text-muted-foreground">Content Sets</div>
        </div>
        <div>
          <div className="text-2xl font-bold text-primary">
            {themes.reduce((sum: number, theme: any) => sum + (theme.statistics?.total_content_items || 0), 0)}
          </div>
          <div className="text-sm text-muted-foreground">Total Items</div>
        </div>
      </div>
    </div>
  ) : null

  return (
    <MainLayout
      title="🎨 Atelier"
      description="Explore themes and discover content collections"
      topContent={topContent}
      bottomContent={bottomContent}
    >
      {/* Themes grid/list */}
      {loading ? (
        <LoadingGrid
          count={8}
          type="theme"
          className={cn(
            viewMode === 'grid'
              ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              : 'grid-cols-1'
          )}
        />
      ) : error ? (
        <EmptyState
          type="error"
          title="Failed to load themes"
          description={error}
          actionLabel="Try again"
          onAction={refetch}
        />
      ) : themes.length === 0 ? (
        <EmptyState
          type={hasActiveFilters ? 'search' : 'themes'}
          actionLabel={hasActiveFilters ? 'Clear filters' : 'Refresh'}
          onAction={hasActiveFilters ? handleClearFilters : refetch}
        />
      ) : (
        <div className={cn(
          'grid gap-6',
          viewMode === 'grid'
            ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
            : 'grid-cols-1 max-w-4xl mx-auto'
        )}>
          {themes.map((theme: any) => (
            <ThemeCard
              key={theme.id}
              theme={theme}
              onClick={handleThemeClick}
              showStats={true}
              className={cn(
                viewMode === 'list' && 'flex-row items-center p-4'
              )}
            />
          ))}
        </div>
      )}
    </MainLayout>
  )
}

export default Atelier
